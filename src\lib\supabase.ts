import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Check if we have valid Supabase credentials
const hasValidCredentials = supabaseUrl && supabaseAnonKey &&
                           supabaseUrl !== 'https://placeholder.supabase.co' &&
                           supabaseAnonKey !== 'placeholder_anon_key' &&
                           supabaseUrl.includes('.supabase.co')

// Client-side Supabase client
export const supabase = hasValidCredentials
  ? createBrowserClient(supabaseUrl, supabaseAnonKey)
  : null

// Server-side Supabase client (for API routes)
export const supabaseAdmin = hasValidCredentials && process.env.SUPABASE_SERVICE_ROLE_KEY
  ? createClient(
      supabaseUrl,
      process.env.SUPABASE_SERVICE_ROLE_KEY,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
  : null

// Database types
export interface FileRecord {
  id: string
  name: string
  size: number
  type: string
  path: string
  parent_id: string | null
  user_id: string
  encrypted_key: string
  checksum: string
  is_folder: boolean
  created_at: string
  updated_at: string
  shared_until?: string
  shared_password?: string
  version: number
}

export interface UserProfile {
  id: string
  email: string
  full_name: string
  avatar_url?: string
  mfa_enabled: boolean
  salt: string
  created_at: string
  updated_at: string
}

export interface ActivityLog {
  id: string
  user_id: string
  action: string
  file_id?: string
  details: Record<string, any>
  ip_address: string
  user_agent: string
  created_at: string
}

export interface ShareLink {
  id: string
  file_id: string
  user_id: string
  token: string
  expires_at: string
  password_hash?: string
  download_count: number
  max_downloads?: number
  created_at: string
}
