import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { supabaseAdmin } from '@/lib/supabase'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase not configured' }, { status: 500 })
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')
    const version = searchParams.get('version')

    if (!fileId) {
      return NextResponse.json({ error: 'File ID required' }, { status: 400 })
    }

    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    let fileRecord: any
    let storagePath: string
    let encryptedKey: string

    if (version) {
      // Get specific version
      const { data: versionRecord, error: versionError } = await supabase
        .from('file_versions')
        .select('*, files!inner(user_id, name)')
        .eq('file_id', fileId)
        .eq('version', parseInt(version))
        .single()

      if (versionError || !versionRecord || versionRecord.files.user_id !== user.id) {
        return NextResponse.json({ error: 'File version not found' }, { status: 404 })
      }

      storagePath = versionRecord.storage_path
      encryptedKey = versionRecord.encrypted_key
      fileRecord = {
        ...versionRecord,
        name: versionRecord.files.name
      }
    } else {
      // Get current version
      const { data: currentFile, error: fileError } = await supabase
        .from('files')
        .select('*')
        .eq('id', fileId)
        .eq('user_id', user.id)
        .single()

      if (fileError || !currentFile) {
        return NextResponse.json({ error: 'File not found' }, { status: 404 })
      }

      if (currentFile.is_folder) {
        return NextResponse.json({ error: 'Cannot download folder' }, { status: 400 })
      }

      storagePath = currentFile.storage_path
      encryptedKey = currentFile.encrypted_key
      fileRecord = currentFile
    }

    // Download encrypted file from storage
    const { data: fileData, error: downloadError } = await supabaseAdmin!.storage
      .from('encrypted-files')
      .download(storagePath)

    if (downloadError || !fileData) {
      console.error('Storage download error:', downloadError)
      return NextResponse.json({ error: 'Failed to download file' }, { status: 500 })
    }

    // Convert blob to array buffer
    const arrayBuffer = await fileData.arrayBuffer()

    // Log download activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: user.id,
        action: 'file_download',
        file_id: fileId,
        details: {
          filename: fileRecord.name,
          version: version ? parseInt(version) : fileRecord.version,
          size: fileRecord.size
        },
        ip_address: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
      })

    // Return encrypted file data with metadata
    return NextResponse.json({
      success: true,
      data: {
        encryptedData: Array.from(new Uint8Array(arrayBuffer)),
        encryptedKey: encryptedKey,
        checksum: fileRecord.checksum,
        filename: fileRecord.name,
        size: fileRecord.size,
        type: fileRecord.type || 'application/octet-stream'
      }
    })

  } catch (error) {
    console.error('Download error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle file streaming for large files
export async function POST(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase not configured' }, { status: 500 })
    }

    const { fileId, chunkIndex, totalChunks } = await request.json()

    if (!fileId || chunkIndex === undefined || !totalChunks) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
    }

    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get file record
    const { data: fileRecord, error: fileError } = await supabase
      .from('files')
      .select('*')
      .eq('id', fileId)
      .eq('user_id', user.id)
      .single()

    if (fileError || !fileRecord) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Download full encrypted file
    const { data: fileData, error: downloadError } = await supabaseAdmin!.storage
      .from('encrypted-files')
      .download(fileRecord.storage_path)

    if (downloadError || !fileData) {
      return NextResponse.json({ error: 'Failed to download file' }, { status: 500 })
    }

    const arrayBuffer = await fileData.arrayBuffer()
    const chunkSize = Math.ceil(arrayBuffer.byteLength / totalChunks)
    const start = chunkIndex * chunkSize
    const end = Math.min(start + chunkSize, arrayBuffer.byteLength)
    const chunk = arrayBuffer.slice(start, end)

    return NextResponse.json({
      success: true,
      data: {
        chunk: Array.from(new Uint8Array(chunk)),
        chunkIndex,
        totalChunks,
        isLastChunk: chunkIndex === totalChunks - 1,
        encryptedKey: chunkIndex === 0 ? fileRecord.encrypted_key : undefined,
        checksum: chunkIndex === 0 ? fileRecord.checksum : undefined,
        filename: chunkIndex === 0 ? fileRecord.name : undefined
      }
    })

  } catch (error) {
    console.error('Chunk download error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
