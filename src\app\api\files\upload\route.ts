import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'
import { supabaseAdmin } from '@/lib/supabase'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Check if Supabase is configured
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Supabase not configured' }, { status: 500 })
    }

    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const encryptedKey = formData.get('encryptedKey') as string
    const checksum = formData.get('checksum') as string
    const parentId = formData.get('parentId') as string | null
    const path = formData.get('path') as string

    if (!file || !encryptedKey || !checksum || !path) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    // Validate file size
    const maxSize = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '104857600') // 100MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File too large' }, { status: 400 })
    }

    // Check if file already exists at this path
    const { data: existingFile } = await supabase
      .from('files')
      .select('id')
      .eq('user_id', user.id)
      .eq('path', path)
      .single()

    if (existingFile) {
      return NextResponse.json({ error: 'File already exists at this path' }, { status: 409 })
    }

    // Generate unique storage path
    const fileExtension = file.name.split('.').pop()
    const timestamp = Date.now()
    const storagePath = `${user.id}/${timestamp}_${crypto.randomUUID()}.${fileExtension}`

    // Upload encrypted file to Supabase Storage
    const { error: uploadError } = await supabaseAdmin.storage
      .from('encrypted-files')
      .upload(storagePath, file, {
        contentType: 'application/octet-stream',
        upsert: false
      })

    if (uploadError) {
      console.error('Storage upload error:', uploadError)
      return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 })
    }

    // Create file record in database
    const { data: fileRecord, error: dbError } = await supabase
      .from('files')
      .insert({
        name: file.name,
        size: file.size,
        type: file.type,
        path: path,
        parent_id: parentId,
        user_id: user.id,
        encrypted_key: encryptedKey,
        checksum: checksum,
        is_folder: false,
        storage_path: storagePath,
        version: 1
      })
      .select()
      .single()

    if (dbError) {
      console.error('Database error:', dbError)
      
      // Clean up uploaded file if database insert fails
      if (supabaseAdmin) {
        await supabaseAdmin.storage
          .from('encrypted-files')
          .remove([storagePath])
      }
      
      return NextResponse.json({ error: 'Failed to create file record' }, { status: 500 })
    }

    // Log activity
    await supabase
      .from('activity_logs')
      .insert({
        user_id: user.id,
        action: 'file_upload',
        file_id: fileRecord.id,
        details: {
          filename: file.name,
          size: file.size,
          type: file.type,
          path: path
        },
        ip_address: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        user_agent: request.headers.get('user-agent') || 'unknown'
      })

    return NextResponse.json({
      success: true,
      file: fileRecord
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Handle file upload with multipart support
export async function PUT(request: NextRequest) {
  try {
    // Create Supabase client with cookies
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll()
          },
          setAll(cookiesToSet) {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          },
        },
      }
    )

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')
    const action = searchParams.get('action')

    if (!fileId) {
      return NextResponse.json({ error: 'File ID required' }, { status: 400 })
    }

    // Get file record
    const { data: fileRecord, error: fileError } = await supabase
      .from('files')
      .select('*')
      .eq('id', fileId)
      .eq('user_id', user.id)
      .single()

    if (fileError || !fileRecord) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    if (action === 'update') {
      // Handle file update (new version)
      const formData = await request.formData()
      const file = formData.get('file') as File
      const encryptedKey = formData.get('encryptedKey') as string
      const checksum = formData.get('checksum') as string

      if (!file || !encryptedKey || !checksum) {
        return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
      }

      // Create version record for old file
      await supabase
        .from('file_versions')
        .insert({
          file_id: fileRecord.id,
          version: fileRecord.version,
          size: fileRecord.size,
          checksum: fileRecord.checksum,
          encrypted_key: fileRecord.encrypted_key,
          storage_path: fileRecord.storage_path
        })

      // Generate new storage path
      const fileExtension = file.name.split('.').pop()
      const timestamp = Date.now()
      const newStoragePath = `${user.id}/${timestamp}_${crypto.randomUUID()}.${fileExtension}`

      // Upload new version
      const { error: uploadError } = await supabaseAdmin!.storage
        .from('encrypted-files')
        .upload(newStoragePath, file, {
          contentType: 'application/octet-stream',
          upsert: false
        })

      if (uploadError) {
        return NextResponse.json({ error: 'Failed to upload new version' }, { status: 500 })
      }

      // Update file record
      const { data: updatedFile, error: updateError } = await supabase
        .from('files')
        .update({
          size: file.size,
          encrypted_key: encryptedKey,
          checksum: checksum,
          storage_path: newStoragePath,
          version: fileRecord.version + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', fileId)
        .select()
        .single()

      if (updateError) {
        // Clean up uploaded file if update fails
        if (supabaseAdmin) {
          await supabaseAdmin.storage
            .from('encrypted-files')
            .remove([newStoragePath])
        }

        return NextResponse.json({ error: 'Failed to update file' }, { status: 500 })
      }

      // Log activity
      await supabase
        .from('activity_logs')
        .insert({
          user_id: user.id,
          action: 'file_update',
          file_id: fileRecord.id,
          details: {
            filename: file.name,
            old_version: fileRecord.version,
            new_version: fileRecord.version + 1,
            size: file.size
          },
          ip_address: request.headers.get('x-forwarded-for') || 'unknown',
          user_agent: request.headers.get('user-agent') || 'unknown'
        })

      return NextResponse.json({
        success: true,
        file: updatedFile
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('File update error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
