{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// File size formatting\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Date formatting\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()\n}\n\n// File type detection\nexport function getFileType(filename: string): string {\n  const extension = filename.split('.').pop()?.toLowerCase()\n  \n  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']\n  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf']\n  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']\n  const audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg']\n  const archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz']\n  const codeTypes = ['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'py', 'java', 'cpp', 'c']\n  \n  if (!extension) return 'unknown'\n  \n  if (imageTypes.includes(extension)) return 'image'\n  if (documentTypes.includes(extension)) return 'document'\n  if (videoTypes.includes(extension)) return 'video'\n  if (audioTypes.includes(extension)) return 'audio'\n  if (archiveTypes.includes(extension)) return 'archive'\n  if (codeTypes.includes(extension)) return 'code'\n  \n  return 'file'\n}\n\n// File icon mapping\nexport function getFileIcon(filename: string, isFolder: boolean = false): string {\n  if (isFolder) return 'folder'\n  \n  const type = getFileType(filename)\n  \n  switch (type) {\n    case 'image': return 'image'\n    case 'document': return 'file-text'\n    case 'video': return 'video'\n    case 'audio': return 'music'\n    case 'archive': return 'archive'\n    case 'code': return 'code'\n    default: return 'file'\n  }\n}\n\n// Validate file type\nexport function isAllowedFileType(file: File): boolean {\n  const allowedTypes = process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES?.split(',') || []\n  \n  return allowedTypes.some(type => {\n    if (type.endsWith('/*')) {\n      const category = type.replace('/*', '')\n      return file.type.startsWith(category)\n    }\n    return file.type === type\n  })\n}\n\n// Validate file size\nexport function isValidFileSize(file: File): boolean {\n  const maxSize = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '104857600') // 100MB default\n  return file.size <= maxSize\n}\n\n// Generate breadcrumb path\nexport function generateBreadcrumbs(path: string): Array<{name: string, path: string}> {\n  if (!path || path === '/') {\n    return [{ name: 'Home', path: '/' }]\n  }\n  \n  const parts = path.split('/').filter(Boolean)\n  const breadcrumbs = [{ name: 'Home', path: '/' }]\n  \n  let currentPath = ''\n  parts.forEach(part => {\n    currentPath += '/' + part\n    breadcrumbs.push({\n      name: part,\n      path: currentPath\n    })\n  })\n  \n  return breadcrumbs\n}\n\n// Sanitize filename\nexport function sanitizeFilename(filename: string): string {\n  return filename\n    .replace(/[^a-zA-Z0-9.-]/g, '_')\n    .replace(/_{2,}/g, '_')\n    .replace(/^_|_$/g, '')\n}\n\n// Generate unique filename\nexport function generateUniqueFilename(originalName: string, existingNames: string[]): string {\n  let name = sanitizeFilename(originalName)\n  let counter = 1\n  \n  while (existingNames.includes(name)) {\n    const extension = originalName.split('.').pop()\n    const baseName = originalName.replace(`.${extension}`, '')\n    name = `${sanitizeFilename(baseName)}_${counter}.${extension}`\n    counter++\n  }\n  \n  return name\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAkEuB;AAlEvB;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,KAAK,MAAM,EAAE,kBAAkB;AAC5D;AAGO,SAAS,YAAY,QAAgB;IAC1C,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAE7C,MAAM,aAAa;QAAC;QAAO;QAAQ;QAAO;QAAO;QAAO;QAAO;KAAO;IACtE,MAAM,gBAAgB;QAAC;QAAO;QAAO;QAAQ;QAAO;KAAM;IAC1D,MAAM,aAAa;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;KAAO;IAC9D,MAAM,aAAa;QAAC;QAAO;QAAO;QAAQ;QAAO;KAAM;IACvD,MAAM,eAAe;QAAC;QAAO;QAAO;QAAM;QAAO;KAAK;IACtD,MAAM,YAAY;QAAC;QAAM;QAAM;QAAO;QAAO;QAAQ;QAAO;QAAM;QAAQ;QAAO;KAAI;IAErF,IAAI,CAAC,WAAW,OAAO;IAEvB,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,cAAc,QAAQ,CAAC,YAAY,OAAO;IAC9C,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,aAAa,QAAQ,CAAC,YAAY,OAAO;IAC7C,IAAI,UAAU,QAAQ,CAAC,YAAY,OAAO;IAE1C,OAAO;AACT;AAGO,SAAS,YAAY,QAAgB,EAAE,WAAoB,KAAK;IACrE,IAAI,UAAU,OAAO;IAErB,MAAM,OAAO,YAAY;IAEzB,OAAQ;QACN,KAAK;YAAS,OAAO;QACrB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAQ,OAAO;QACpB;YAAS,OAAO;IAClB;AACF;AAGO,SAAS,kBAAkB,IAAU;IAC1C,MAAM,eAAe,iHAA4C,MAAM,QAAQ,EAAE;IAEjF,OAAO,aAAa,IAAI,CAAC,CAAA;QACvB,IAAI,KAAK,QAAQ,CAAC,OAAO;YACvB,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM;YACpC,OAAO,KAAK,IAAI,CAAC,UAAU,CAAC;QAC9B;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;AACF;AAGO,SAAS,gBAAgB,IAAU;IACxC,MAAM,UAAU,SAAS,iDAAyC,aAAa,gBAAgB;;IAC/F,OAAO,KAAK,IAAI,IAAI;AACtB;AAGO,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,QAAQ,SAAS,KAAK;QACzB,OAAO;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAI;SAAE;IACtC;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC;IACrC,MAAM,cAAc;QAAC;YAAE,MAAM;YAAQ,MAAM;QAAI;KAAE;IAEjD,IAAI,cAAc;IAClB,MAAM,OAAO,CAAC,CAAA;QACZ,eAAe,MAAM;QACrB,YAAY,IAAI,CAAC;YACf,MAAM;YACN,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SACJ,OAAO,CAAC,mBAAmB,KAC3B,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,UAAU;AACvB;AAGO,SAAS,uBAAuB,YAAoB,EAAE,aAAuB;IAClF,IAAI,OAAO,iBAAiB;IAC5B,IAAI,UAAU;IAEd,MAAO,cAAc,QAAQ,CAAC,MAAO;QACnC,MAAM,YAAY,aAAa,KAAK,CAAC,KAAK,GAAG;QAC7C,MAAM,WAAW,aAAa,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;QACvD,OAAO,GAAG,iBAAiB,UAAU,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;QAC9D;IACF;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Shield, Lock, FileText, Users, CheckCircle } from 'lucide-react'\n\nexport default function Home() {\n  const { user, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!loading && user) {\n      router.push('/dashboard')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Shield className=\"h-8 w-8 text-blue-600\" />\n              <span className=\"ml-2 text-xl font-bold text-gray-900\">Secure File Explorer</span>\n            </div>\n            <div className=\"flex space-x-4\">\n              <Link href=\"/auth/login\">\n                <Button variant=\"ghost\">Sign In</Button>\n              </Link>\n              <Link href=\"/auth/register\">\n                <Button>Get Started</Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Hero Section */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl\">\n            <span className=\"block\">Secure File Storage</span>\n            <span className=\"block text-blue-600\">Made Simple</span>\n          </h1>\n          <p className=\"mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl\">\n            Store, manage, and share your files with military-grade encryption.\n            Your data is protected with end-to-end encryption and advanced security features.\n          </p>\n          <div className=\"mt-5 max-w-md mx-auto sm:flex sm:justify-center md:mt-8\">\n            <div className=\"rounded-md shadow\">\n              <Link href=\"/auth/register\">\n                <Button size=\"lg\" className=\"w-full\">\n                  Start Free Trial\n                </Button>\n              </Link>\n            </div>\n            <div className=\"mt-3 rounded-md shadow sm:mt-0 sm:ml-3\">\n              <Link href=\"/auth/login\">\n                <Button variant=\"outline\" size=\"lg\" className=\"w-full\">\n                  Sign In\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n\n        {/* Features */}\n        <div className=\"mt-20\">\n          <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n            <Card>\n              <CardHeader>\n                <Lock className=\"h-8 w-8 text-blue-600\" />\n                <CardTitle>End-to-End Encryption</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Your files are encrypted with AES-256 before leaving your device.\n                  Only you have the keys to decrypt your data.\n                </CardDescription>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <FileText className=\"h-8 w-8 text-blue-600\" />\n                <CardTitle>File Management</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Organize your files with folders, search, and filtering.\n                  Preview common file types directly in your browser.\n                </CardDescription>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <Users className=\"h-8 w-8 text-blue-600\" />\n                <CardTitle>Secure Sharing</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <CardDescription>\n                  Share files securely with time-limited links and password protection.\n                  Control who can access your files and for how long.\n                </CardDescription>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        {/* Security Features */}\n        <div className=\"mt-20\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl font-extrabold text-gray-900\">\n              Enterprise-Grade Security\n            </h2>\n            <p className=\"mt-4 text-lg text-gray-500\">\n              Your security is our top priority. We use the latest encryption and security practices.\n            </p>\n          </div>\n          <div className=\"mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2\">\n            <div className=\"flex items-start\">\n              <CheckCircle className=\"flex-shrink-0 h-6 w-6 text-green-500\" />\n              <div className=\"ml-3\">\n                <h3 className=\"text-lg font-medium text-gray-900\">AES-256 Encryption</h3>\n                <p className=\"text-gray-500\">Military-grade encryption for all your files</p>\n              </div>\n            </div>\n            <div className=\"flex items-start\">\n              <CheckCircle className=\"flex-shrink-0 h-6 w-6 text-green-500\" />\n              <div className=\"ml-3\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Zero-Knowledge Architecture</h3>\n                <p className=\"text-gray-500\">We can't see your data, even if we wanted to</p>\n              </div>\n            </div>\n            <div className=\"flex items-start\">\n              <CheckCircle className=\"flex-shrink-0 h-6 w-6 text-green-500\" />\n              <div className=\"ml-3\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Multi-Factor Authentication</h3>\n                <p className=\"text-gray-500\">Extra layer of security for your account</p>\n              </div>\n            </div>\n            <div className=\"flex items-start\">\n              <CheckCircle className=\"flex-shrink-0 h-6 w-6 text-green-500\" />\n              <div className=\"ml-3\">\n                <h3 className=\"text-lg font-medium text-gray-900\">Activity Monitoring</h3>\n                <p className=\"text-gray-500\">Track all access to your files</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white mt-20\">\n        <div className=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <div className=\"flex items-center justify-center\">\n              <Shield className=\"h-6 w-6 text-blue-600\" />\n              <span className=\"ml-2 text-lg font-semibold text-gray-900\">Secure File Explorer</span>\n            </div>\n            <p className=\"mt-2 text-gray-500\">\n              © 2024 Secure File Explorer. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW,MAAM;gBACpB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAuC;;;;;;;;;;;;0CAEzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;sDAAQ;;;;;;;;;;;kDAE1B,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlB,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAK,WAAU;kDAAQ;;;;;;kDACxB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;0CAExC,6LAAC;gCAAE,WAAU;0CAA2F;;;;;;0CAIxG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;kDAKzC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAOrB,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;8CAOrB,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;sDAEb,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAI5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAGjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAGjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;kDAGjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvC,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;;;;;;;0CAE7D,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GA5KwB;;QACI,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}