{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_574bffa5._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_6e7324c0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KzrtSYEFbLXUtA9qFJrfNR2JasCj+WoLCCfWcRC4PlU=", "__NEXT_PREVIEW_MODE_ID": "d8da5051804de7428a3c65b3f5e635eb", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b6529faab5062e872ac8bb45103821b9b8c85d0080ffdb644a4d7bc35ffd0fd9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "693ba95b61dde34ace61a02f0ad8bc34551a88ae61978f260217375a2346d1df"}}}, "instrumentation": null, "functions": {}}