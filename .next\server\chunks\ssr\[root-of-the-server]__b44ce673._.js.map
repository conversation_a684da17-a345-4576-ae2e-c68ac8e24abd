{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// File size formatting\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\n// Date formatting\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date)\n  return d.toLocaleDateString() + ' ' + d.toLocaleTimeString()\n}\n\n// File type detection\nexport function getFileType(filename: string): string {\n  const extension = filename.split('.').pop()?.toLowerCase()\n  \n  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp']\n  const documentTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf']\n  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']\n  const audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg']\n  const archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz']\n  const codeTypes = ['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'py', 'java', 'cpp', 'c']\n  \n  if (!extension) return 'unknown'\n  \n  if (imageTypes.includes(extension)) return 'image'\n  if (documentTypes.includes(extension)) return 'document'\n  if (videoTypes.includes(extension)) return 'video'\n  if (audioTypes.includes(extension)) return 'audio'\n  if (archiveTypes.includes(extension)) return 'archive'\n  if (codeTypes.includes(extension)) return 'code'\n  \n  return 'file'\n}\n\n// File icon mapping\nexport function getFileIcon(filename: string, isFolder: boolean = false): string {\n  if (isFolder) return 'folder'\n  \n  const type = getFileType(filename)\n  \n  switch (type) {\n    case 'image': return 'image'\n    case 'document': return 'file-text'\n    case 'video': return 'video'\n    case 'audio': return 'music'\n    case 'archive': return 'archive'\n    case 'code': return 'code'\n    default: return 'file'\n  }\n}\n\n// Validate file type\nexport function isAllowedFileType(file: File): boolean {\n  const allowedTypes = process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES?.split(',') || []\n  \n  return allowedTypes.some(type => {\n    if (type.endsWith('/*')) {\n      const category = type.replace('/*', '')\n      return file.type.startsWith(category)\n    }\n    return file.type === type\n  })\n}\n\n// Validate file size\nexport function isValidFileSize(file: File): boolean {\n  const maxSize = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '104857600') // 100MB default\n  return file.size <= maxSize\n}\n\n// Generate breadcrumb path\nexport function generateBreadcrumbs(path: string): Array<{name: string, path: string}> {\n  if (!path || path === '/') {\n    return [{ name: 'Home', path: '/' }]\n  }\n  \n  const parts = path.split('/').filter(Boolean)\n  const breadcrumbs = [{ name: 'Home', path: '/' }]\n  \n  let currentPath = ''\n  parts.forEach(part => {\n    currentPath += '/' + part\n    breadcrumbs.push({\n      name: part,\n      path: currentPath\n    })\n  })\n  \n  return breadcrumbs\n}\n\n// Sanitize filename\nexport function sanitizeFilename(filename: string): string {\n  return filename\n    .replace(/[^a-zA-Z0-9.-]/g, '_')\n    .replace(/_{2,}/g, '_')\n    .replace(/^_|_$/g, '')\n}\n\n// Generate unique filename\nexport function generateUniqueFilename(originalName: string, existingNames: string[]): string {\n  let name = sanitizeFilename(originalName)\n  let counter = 1\n  \n  while (existingNames.includes(name)) {\n    const extension = originalName.split('.').pop()\n    const baseName = originalName.replace(`.${extension}`, '')\n    name = `${sanitizeFilename(baseName)}_${counter}.${extension}`\n    counter++\n  }\n  \n  return name\n}\n\n// Debounce function\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  \n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n// Copy to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text)\n    return true\n  } catch (error) {\n    console.error('Failed to copy to clipboard:', error)\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAGO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,KAAK,MAAM,EAAE,kBAAkB;AAC5D;AAGO,SAAS,YAAY,QAAgB;IAC1C,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAE7C,MAAM,aAAa;QAAC;QAAO;QAAQ;QAAO;QAAO;QAAO;QAAO;KAAO;IACtE,MAAM,gBAAgB;QAAC;QAAO;QAAO;QAAQ;QAAO;KAAM;IAC1D,MAAM,aAAa;QAAC;QAAO;QAAO;QAAO;QAAO;QAAO;KAAO;IAC9D,MAAM,aAAa;QAAC;QAAO;QAAO;QAAQ;QAAO;KAAM;IACvD,MAAM,eAAe;QAAC;QAAO;QAAO;QAAM;QAAO;KAAK;IACtD,MAAM,YAAY;QAAC;QAAM;QAAM;QAAO;QAAO;QAAQ;QAAO;QAAM;QAAQ;QAAO;KAAI;IAErF,IAAI,CAAC,WAAW,OAAO;IAEvB,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,cAAc,QAAQ,CAAC,YAAY,OAAO;IAC9C,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,WAAW,QAAQ,CAAC,YAAY,OAAO;IAC3C,IAAI,aAAa,QAAQ,CAAC,YAAY,OAAO;IAC7C,IAAI,UAAU,QAAQ,CAAC,YAAY,OAAO;IAE1C,OAAO;AACT;AAGO,SAAS,YAAY,QAAgB,EAAE,WAAoB,KAAK;IACrE,IAAI,UAAU,OAAO;IAErB,MAAM,OAAO,YAAY;IAEzB,OAAQ;QACN,KAAK;YAAS,OAAO;QACrB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAQ,OAAO;QACpB;YAAS,OAAO;IAClB;AACF;AAGO,SAAS,kBAAkB,IAAU;IAC1C,MAAM,eAAe,iHAA4C,MAAM,QAAQ,EAAE;IAEjF,OAAO,aAAa,IAAI,CAAC,CAAA;QACvB,IAAI,KAAK,QAAQ,CAAC,OAAO;YACvB,MAAM,WAAW,KAAK,OAAO,CAAC,MAAM;YACpC,OAAO,KAAK,IAAI,CAAC,UAAU,CAAC;QAC9B;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;AACF;AAGO,SAAS,gBAAgB,IAAU;IACxC,MAAM,UAAU,SAAS,iDAAyC,aAAa,gBAAgB;;IAC/F,OAAO,KAAK,IAAI,IAAI;AACtB;AAGO,SAAS,oBAAoB,IAAY;IAC9C,IAAI,CAAC,QAAQ,SAAS,KAAK;QACzB,OAAO;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAI;SAAE;IACtC;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC;IACrC,MAAM,cAAc;QAAC;YAAE,MAAM;YAAQ,MAAM;QAAI;KAAE;IAEjD,IAAI,cAAc;IAClB,MAAM,OAAO,CAAC,CAAA;QACZ,eAAe,MAAM;QACrB,YAAY,IAAI,CAAC;YACf,MAAM;YACN,MAAM;QACR;IACF;IAEA,OAAO;AACT;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SACJ,OAAO,CAAC,mBAAmB,KAC3B,OAAO,CAAC,UAAU,KAClB,OAAO,CAAC,UAAU;AACvB;AAGO,SAAS,uBAAuB,YAAoB,EAAE,aAAuB;IAClF,IAAI,OAAO,iBAAiB;IAC5B,IAAI,UAAU;IAEd,MAAO,cAAc,QAAQ,CAAC,MAAO;QACnC,MAAM,YAAY,aAAa,KAAK,CAAC,KAAK,GAAG;QAC7C,MAAM,WAAW,aAAa,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;QACvD,OAAO,GAAG,iBAAiB,UAAU,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;QAC9D;IACF;IAEA,OAAO;AACT;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/components/ui/Input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/components/ui/Alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/app/auth/register/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Alert, AlertDescription } from '@/components/ui/Alert'\nimport { Lock, Mail, User, Eye, EyeOff } from 'lucide-react'\nimport toast from 'react-hot-toast'\n\nexport default function RegisterPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [confirmPassword, setConfirmPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n\n  const { signUp } = useAuth()\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    // Validation\n    if (password !== confirmPassword) {\n      setError('Passwords do not match')\n      setLoading(false)\n      return\n    }\n\n    if (password.length < 8) {\n      setError('Password must be at least 8 characters long')\n      setLoading(false)\n      return\n    }\n\n    try {\n      const { error } = await signUp(email, password, fullName)\n      \n      if (error) {\n        setError(error.message)\n        toast.error('Registration failed')\n      } else {\n        toast.success('Registration successful! Please check your email to verify your account.')\n        router.push('/auth/login')\n      }\n    } catch (err) {\n      setError('An unexpected error occurred')\n      toast.error('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div className=\"text-center\">\n          <Lock className=\"mx-auto h-12 w-12 text-blue-600\" />\n          <h2 className=\"mt-6 text-3xl font-extrabold text-gray-900\">\n            Create your account\n          </h2>\n          <p className=\"mt-2 text-sm text-gray-600\">\n            Join the secure file explorer\n          </p>\n        </div>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>Get started</CardTitle>\n            <CardDescription>\n              Create your account to start managing your files securely\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              {error && (\n                <Alert variant=\"destructive\">\n                  <AlertDescription>{error}</AlertDescription>\n                </Alert>\n              )}\n\n              <div>\n                <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700\">\n                  Full Name\n                </label>\n                <div className=\"mt-1 relative\">\n                  <Input\n                    id=\"fullName\"\n                    name=\"fullName\"\n                    type=\"text\"\n                    autoComplete=\"name\"\n                    required\n                    value={fullName}\n                    onChange={(e) => setFullName(e.target.value)}\n                    className=\"pl-10\"\n                    placeholder=\"Enter your full name\"\n                  />\n                  <User className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                  Email address\n                </label>\n                <div className=\"mt-1 relative\">\n                  <Input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    autoComplete=\"email\"\n                    required\n                    value={email}\n                    onChange={(e) => setEmail(e.target.value)}\n                    className=\"pl-10\"\n                    placeholder=\"Enter your email\"\n                  />\n                  <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                  Password\n                </label>\n                <div className=\"mt-1 relative\">\n                  <Input\n                    id=\"password\"\n                    name=\"password\"\n                    type={showPassword ? 'text' : 'password'}\n                    autoComplete=\"new-password\"\n                    required\n                    value={password}\n                    onChange={(e) => setPassword(e.target.value)}\n                    className=\"pl-10 pr-10\"\n                    placeholder=\"Enter your password\"\n                  />\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <button\n                    type=\"button\"\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeOff className=\"h-4 w-4 text-gray-400\" />\n                    ) : (\n                      <Eye className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                  Confirm Password\n                </label>\n                <div className=\"mt-1 relative\">\n                  <Input\n                    id=\"confirmPassword\"\n                    name=\"confirmPassword\"\n                    type={showConfirmPassword ? 'text' : 'password'}\n                    autoComplete=\"new-password\"\n                    required\n                    value={confirmPassword}\n                    onChange={(e) => setConfirmPassword(e.target.value)}\n                    className=\"pl-10 pr-10\"\n                    placeholder=\"Confirm your password\"\n                  />\n                  <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                  <button\n                    type=\"button\"\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2\"\n                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                  >\n                    {showConfirmPassword ? (\n                      <EyeOff className=\"h-4 w-4 text-gray-400\" />\n                    ) : (\n                      <Eye className=\"h-4 w-4 text-gray-400\" />\n                    )}\n                  </button>\n                </div>\n              </div>\n\n              <Button\n                type=\"submit\"\n                className=\"w-full\"\n                disabled={loading}\n              >\n                {loading ? 'Creating account...' : 'Create account'}\n              </Button>\n            </form>\n\n            <div className=\"mt-6 text-center\">\n              <p className=\"text-sm text-gray-600\">\n                Already have an account?{' '}\n                <Link\n                  href=\"/auth/login\"\n                  className=\"font-medium text-blue-600 hover:text-blue-500\"\n                >\n                  Sign in\n                </Link>\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,aAAa;QACb,IAAI,aAAa,iBAAiB;YAChC,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,SAAS;YACT,WAAW;YACX;QACF;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO,UAAU;YAEhD,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;gBACtB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBACd,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAG3D,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;;8CACT,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;8CACX,8OAAC,gIAAA,CAAA,kBAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,gIAAA,CAAA,cAAW;;8CACV,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;wCACrC,uBACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDACb,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;0DAAE;;;;;;;;;;;sDAIvB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,cAAa;4DACb,QAAQ;4DACR,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAIpB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA0C;;;;;;8DAG3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,cAAa;4DACb,QAAQ;4DACR,OAAO;4DACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4DACxC,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAIpB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA0C;;;;;;8DAG9E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAM,eAAe,SAAS;4DAC9B,cAAa;4DACb,QAAQ;4DACR,OAAO;4DACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,gBAAgB,CAAC;sEAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAkB,WAAU;8DAA0C;;;;;;8DAGrF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAM,sBAAsB,SAAS;4DACrC,cAAa;4DACb,QAAQ;4DACR,OAAO;4DACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;4DAClD,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,uBAAuB,CAAC;sEAEtC,oCACC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;qFAElB,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMvB,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;sDAET,UAAU,wBAAwB;;;;;;;;;;;;8CAIvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAAwB;4CACV;0DACzB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}