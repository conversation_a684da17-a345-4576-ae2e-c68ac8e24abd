{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n// Check if we have valid Supabase credentials\nconst hasValidCredentials = supabaseUrl && supabaseAnonKey &&\n                           supabaseUrl !== 'https://placeholder.supabase.co' &&\n                           supabaseAnonKey !== 'placeholder_anon_key' &&\n                           supabaseUrl.includes('.supabase.co')\n\n// Client-side Supabase client\nexport const supabase = hasValidCredentials\n  ? createBrowserClient(supabaseUrl, supabaseAnonKey)\n  : null\n\n// Server-side Supabase client (for API routes)\nexport const supabaseAdmin = hasValidCredentials && process.env.SUPABASE_SERVICE_ROLE_KEY\n  ? createClient(\n      supabaseUrl,\n      process.env.SUPABASE_SERVICE_ROLE_KEY,\n      {\n        auth: {\n          autoRefreshToken: false,\n          persistSession: false\n        }\n      }\n    )\n  : null\n\n// Database types\nexport interface FileRecord {\n  id: string\n  name: string\n  size: number\n  type: string\n  path: string\n  parent_id: string | null\n  user_id: string\n  encrypted_key: string\n  checksum: string\n  is_folder: boolean\n  created_at: string\n  updated_at: string\n  shared_until?: string\n  shared_password?: string\n  version: number\n}\n\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name: string\n  avatar_url?: string\n  mfa_enabled: boolean\n  salt: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ActivityLog {\n  id: string\n  user_id: string\n  action: string\n  file_id?: string\n  details: Record<string, any>\n  ip_address: string\n  user_agent: string\n  created_at: string\n}\n\nexport interface ShareLink {\n  id: string\n  file_id: string\n  user_id: string\n  token: string\n  expires_at: string\n  password_hash?: string\n  download_count: number\n  max_downloads?: number\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEN,8CAA8C;AAC9C,MAAM,sBAAsB,eAAe,mBAChB,gBAAgB,qCAChB,oBAAoB,0BACpB,YAAY,QAAQ,CAAC;AAGzC,MAAM,WAAW,sBACpB,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,mBACjC;AAGG,MAAM,gBAAgB,uBAAuB,QAAQ,GAAG,CAAC,yBAAyB,GACrF,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACT,aACA,QAAQ,GAAG,CAAC,yBAAyB,EACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF,KAEF", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Secure%20File%20Explorer/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\nimport { UserProfile } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  profile: UserProfile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<{ error: any }>\n  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [profile, setProfile] = useState<UserProfile | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Check if Supabase is available\n    if (!supabase) {\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      if (!supabase) return\n      const { data: { session } } = await supabase.auth.getSession()\n      setSession(session)\n      setUser(session?.user ?? null)\n\n      if (session?.user) {\n        await fetchProfile(session.user.id)\n      }\n\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    let subscription: any = null\n    if (supabase) {\n      const { data } = supabase.auth.onAuthStateChange(\n        async (_, session) => {\n          setSession(session)\n          setUser(session?.user ?? null)\n\n          if (session?.user) {\n            await fetchProfile(session.user.id)\n          } else {\n            setProfile(null)\n          }\n\n          setLoading(false)\n        }\n      )\n      subscription = data.subscription\n    }\n\n    return () => {\n      if (subscription) {\n        subscription.unsubscribe()\n      }\n    }\n  }, [])\n\n  const fetchProfile = async (userId: string) => {\n    if (!supabase) return\n\n    try {\n      const { data, error } = await supabase\n        .from('user_profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) throw error\n      setProfile(data)\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    if (!supabase) return\n    await supabase.auth.signOut()\n  }\n\n  const resetPassword = async (email: string) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/auth/reset-password`,\n    })\n    return { error }\n  }\n\n  const updateProfile = async (updates: Partial<UserProfile>) => {\n    if (!supabase) return { error: new Error('Supabase not configured') }\n    if (!user) return { error: new Error('No user logged in') }\n\n    const { error } = await supabase\n      .from('user_profiles')\n      .update(updates)\n      .eq('id', user.id)\n\n    if (!error) {\n      setProfile(prev => prev ? { ...prev, ...updates } : null)\n    }\n\n    return { error }\n  }\n\n  const value = {\n    user,\n    session,\n    profile,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n    updateProfile,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAmBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACb,WAAW;YACX;QACF;QAEA,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;YACf,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAC5D,WAAW;YACX,QAAQ,SAAS,QAAQ;YAEzB,IAAI,SAAS,MAAM;gBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;YACpC;YAEA,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,IAAI,eAAoB;QACxB,IAAI,sHAAA,CAAA,WAAQ,EAAE;YACZ,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAC9C,OAAO,GAAG;gBACR,WAAW;gBACX,QAAQ,SAAS,QAAQ;gBAEzB,IAAI,SAAS,MAAM;oBACjB,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;gBACpC,OAAO;oBACL,WAAW;gBACb;gBAEA,WAAW;YACb;YAEF,eAAe,KAAK,YAAY;QAClC;QAEA,OAAO;YACL,IAAI,cAAc;gBAChB,aAAa,WAAW;YAC1B;QACF;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;QAEf,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO,MAAM;YACjB,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;QAEpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;QAEpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE;QACf,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;QAEpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;QAC7D;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,CAAC,sHAAA,CAAA,WAAQ,EAAE,OAAO;YAAE,OAAO,IAAI,MAAM;QAA2B;QACpE,IAAI,CAAC,MAAM,OAAO;YAAE,OAAO,IAAI,MAAM;QAAqB;QAE1D,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,iBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,CAAC,OAAO;YACV,WAAW,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC,IAAI;QACtD;QAEA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}